import { Types } from "mongoose";
import logger from "../../common/logger/log.module";
import { connectToMongo, closeConnection } from "../../common/database/db.module";
import { Service } from "./service.model";
import { AppointmentType } from "./appointment-type.model";
import { parseCSV } from "../../common/utils/csv-parser";

interface ICsvAppointmentType {
  id: string;
  serviceId: string;
  name: string;
  durationInMinutes: number;
  isActive?: boolean;
}

/**
 * Validate appointment type data from CSV
 * @param csvAppointmentType Appointment type data from CSV
 * @returns Array of validation error messages
 */
function validateAppointmentTypeData(csvAppointmentType: ICsvAppointmentType): string[] {
  const errors: string[] = [];

  if (!csvAppointmentType.id) {
    errors.push("Appointment type ID is required");
  }

  if (!csvAppointmentType.serviceId) {
    errors.push("Service ID is required");
  }

  if (!csvAppointmentType.name) {
    errors.push("Appointment type name is required");
  }

  if (csvAppointmentType.durationInMinutes === undefined) {
    errors.push("Duration in minutes is required");
  } else if (isNaN(csvAppointmentType.durationInMinutes) || csvAppointmentType.durationInMinutes <= 0) {
    errors.push("Duration in minutes must be a positive number");
  }

  return errors;
}

/**
 * Migrate appointment types data from CSV to MongoDB
 * This function creates standalone appointment types and adds them to existing services
 */
export async function migrateAppointmentTypes(_dbName: string = "hop-migration"): Promise<void> {
  let session: any = null;

  try {
    logger.log("Starting appointment types migration...");

    // Connect to database
    await connectToMongo();

    // Start a session for transaction
    const mongoose = require("mongoose");
    session = await mongoose.startSession();
    session.startTransaction();

    try {
      // Get appointment types data from CSV
      const csvAppointmentTypes = await parseCSV<ICsvAppointmentType>("data/appointment-types.csv");

      // Validate data
      const validationErrors: { [key: string]: string[] } = {};
      csvAppointmentTypes.forEach((appointmentType, index) => {
        const errors = validateAppointmentTypeData(appointmentType);
        if (errors.length > 0) {
          validationErrors[`Row ${index + 1}`] = errors;
        }
      });

      if (Object.keys(validationErrors).length > 0) {
        logger.error("Validation errors in appointment types data:", validationErrors);
        throw new Error("Invalid appointment types data in CSV");
      }

      // Create standalone appointment types
      const appointmentTypeDocuments = csvAppointmentTypes.map(type => ({
        name: type.name,
        durationInMinutes: type.durationInMinutes,
        onlineBookingAllowed: true, // Default to true
        isActive: type.isActive !== undefined ? type.isActive : true,
        image: "",
        isFeatured: false,
        serviceId: new Types.ObjectId(type.serviceId),
        createdBy: new Types.ObjectId(global.config.organization),
        organizationId: new Types.ObjectId(global.config.organization),
      }));

      // Insert appointment types
      const insertedTypes = await AppointmentType.insertMany(appointmentTypeDocuments, { session });
      logger.log(`Successfully created ${insertedTypes.length} standalone appointment types`);

      // Group appointment types by service ID
      const appointmentTypesByService: { [serviceId: string]: ICsvAppointmentType[] } = {};

      csvAppointmentTypes.forEach(appointmentType => {
        if (appointmentType.serviceId) {
          if (!appointmentTypesByService[appointmentType.serviceId]) {
            appointmentTypesByService[appointmentType.serviceId] = [];
          }
          appointmentTypesByService[appointmentType.serviceId].push(appointmentType);
        }
      });

      // Update services with appointment types
      for (const [serviceId, appointmentTypes] of Object.entries(appointmentTypesByService)) {
        const formattedAppointmentTypes = appointmentTypes.map(type => ({
          name: type.name,
          durationInMinutes: type.durationInMinutes,
          onlineBookingAllowed: true, // Default to true
          isActive: type.isActive !== undefined ? type.isActive : true,
          image: "",
          isFeatured: false
        }));

        await Service.findByIdAndUpdate(
          serviceId,
          { $push: { appointmentType: { $each: formattedAppointmentTypes } } },
          { session }
        );
      }

      // Commit the transaction
      await session.commitTransaction();
      logger.log("Transaction committed successfully");
      logger.log(`Successfully migrated appointment types to MongoDB as standalone entities and added them to services`);
    } catch (error) {
      // Abort the transaction on error
      if (session) {
        await session.abortTransaction();
      }
      logger.error("Error in migration process:", error);
      throw error;
    }
  } catch (error) {
    logger.error("Error migrating appointment types:", error);
  } finally {
    // End the session
    if (session) {
      session.endSession();
    }
    // Close the connection
    await closeConnection();
  }
}
