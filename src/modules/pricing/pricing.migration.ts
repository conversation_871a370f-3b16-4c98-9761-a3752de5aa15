import { Types } from "mongoose";
import logger from "../../common/logger/log.module";
import { connectToMongo, closeConnection } from "../../common/database/db.module";
import { Pricing, ClassType, SessionType, DurationUnit } from "./pricing.model";
import { AppointmentType } from "./appointment-type.model";
import { Service } from "./service.model";
import { parseCSV } from "../../common/utils/csv-parser";

interface ICsvAppointmentTypeEntry {
    name?: string;
    durationInMinutes?: number;
    onlineBookingAllowed?: boolean;
    isActive?: boolean;
    image?: string;
    isFeatured?: boolean;
}

interface ICsvServices {
    type: ClassType;
    name?: string;
    serviceCategory: string;
    sessionType: SessionType;
    sessionCount: number;
    dayPassLimit: number;
    sessionPerDay?: number;
    introductoryOffer?: string;
    appointmentType?: ICsvAppointmentTypeEntry[]; // Array of appointment types
}

interface ICsvPricing {
    id: number;
    name: string;
    price: number;
    tax: number;
    durationUnit: DurationUnit;
    isSellOnline: boolean;
    expiredInDays: number;
    hsnOrSacCode?: string;
    isActive?: boolean;
    services?: ICsvServices;
    revenueCategory?: string;
}

/**
 * Validate pricing data from CSV
 * @param csvPricing Pricing data from CSV
 * @returns Array of validation error messages
 */
function validatePricingData(csvPricing: ICsvPricing): string[] {
    const errors: string[] = [];

    if (!csvPricing.name) {
        errors.push("Pricing name is required");
    }

    if (csvPricing.price === undefined) {
        errors.push("Price is required");
    } else if (isNaN(csvPricing.price) || csvPricing.price < 0) {
        errors.push("Price must be a non-negative number");
    }

    if (csvPricing.tax === undefined) {
        errors.push("Tax is required");
    } else if (isNaN(csvPricing.tax) || csvPricing.tax < 0) {
        errors.push("Tax must be a non-negative number");
    }

    if (!csvPricing.durationUnit) {
        errors.push("Duration unit is required");
    }

    if (csvPricing.isSellOnline === undefined) {
        errors.push("isSellOnline flag is required");
    }

    if (csvPricing.expiredInDays === undefined) {
        errors.push("Expired in days is required");
    } else if (isNaN(csvPricing.expiredInDays) || csvPricing.expiredInDays <= 0) {
        errors.push("Expired in days must be a positive number");
    }

    // Validate services if provided
    if (csvPricing.services) {
        if (!csvPricing.services.type) {
            errors.push("Service type is required");
        } else if (!Object.values(ClassType).includes(csvPricing.services.type as ClassType)) {
            errors.push(`Invalid service type: ${csvPricing.services.type}. Valid values are: ${Object.values(ClassType).join(', ')}`);
        }

        if (!csvPricing.services.serviceCategory) {
            errors.push("Service category is required");
        }

        if (!csvPricing.services.sessionType) {
            errors.push("Session type is required");
        } else if (!Object.values(SessionType).includes(csvPricing.services.sessionType as SessionType)) {
            errors.push(`Invalid session type: ${csvPricing.services.sessionType}. Valid values are: ${Object.values(SessionType).join(', ')}`);
        }

        if (csvPricing.services.sessionCount === undefined) {
            errors.push("Session count is required");
        } else if (isNaN(csvPricing.services.sessionCount) || csvPricing.services.sessionCount < 0) {
            errors.push("Session count must be a non-negative number");
        }

        if (csvPricing.services.dayPassLimit === undefined) {
            errors.push("Day pass limit is required");
        } else if (isNaN(csvPricing.services.dayPassLimit) || csvPricing.services.dayPassLimit < 0) {
            errors.push("Day pass limit must be a non-negative number");
        }

        // Validate appointment types if provided
        if (csvPricing.services.appointmentType && csvPricing.services.appointmentType.length > 0) {
            csvPricing.services.appointmentType.forEach((type, index) => {
                if (!type.name) {
                    errors.push(`Appointment type at index ${index} is missing name`);
                }
                if (type.durationInMinutes === undefined) {
                    errors.push(`Appointment type at index ${index} is missing durationInMinutes`);
                }
                // onlineBookingAllowed is optional in the new format, defaulting to true
            });
        }
    }

    return errors;
}

export async function migratePricing(_dbName: string = "hop-migration"): Promise<void> {
    let session: any = null;

    try {
        logger.log("Starting pricing migration...");

        // Connect to database
        const mongoose = await connectToMongo();

        // Start a session for transaction
        session = await mongoose.startSession();
        session.startTransaction();

        try {
            // Get pricing data from CSV or other source
            const csvPricings = await parseCSV<ICsvPricing>("data/pricing.csv");

            // Validate data
            const validationErrors: { [key: string]: string[] } = {};
            csvPricings.forEach((pricing, index) => {
                const errors = validatePricingData(pricing);
                if (errors.length > 0) {
                    validationErrors[`Row ${index + 1}`] = errors;
                }
            });

            if (Object.keys(validationErrors).length > 0) {
                logger.error("Validation errors in pricing data:", validationErrors);
                throw new Error("Invalid pricing data in CSV");
            }

            // Transform data for MongoDB
            const appointmentTypeDocs = [];
            const appointmentTypeNameMap = new Map();
            const servicesDocs = []
            const servicesNameMap = new Map();

            const pricings = csvPricings.map((csvPricing) => {
                const pricingData = new Pricing({
                    id: csvPricing.id,
                    name: csvPricing.name,
                    price: csvPricing.price,
                    tax: csvPricing.tax,
                    durationUnit: csvPricing.durationUnit,
                    isSellOnline: csvPricing.isSellOnline,
                    expiredInDays: csvPricing.expiredInDays,
                    hsnOrSacCode: csvPricing.hsnOrSacCode,
                    isActive: csvPricing.isActive !== undefined ? csvPricing.isActive : true,
                    createdBy: new Types.ObjectId(global.config.organization),
                    revenueCategory: csvPricing.revenueCategory,
                    organizationId: new Types.ObjectId(global.config.organization),
                });
                // Add services if provided
                if (csvPricing.services) {
                    // Process appointment types
                    const appointmentTypes: ICsvAppointmentTypeEntry[] = Array.isArray(csvPricing.services.appointmentType) ? csvPricing.services.appointmentType : Object.values(csvPricing.services.appointmentType);

                    // Format appointment types for MongoDB
                    const formattedAppointmentTypes = [];
                    for (const type of appointmentTypes.filter(type => type.name)){
                        let subType = appointmentTypeNameMap.get(type.name);
                        if (!subType) {
                            subType = new AppointmentType({
                                name: type.name,
                                durationInMinutes: type.durationInMinutes,
                                onlineBookingAllowed: type.onlineBookingAllowed !== undefined ? type.onlineBookingAllowed : true,
                                isActive: type.isActive !== undefined ? type.isActive : true,
                                image: type.image || '',
                                isFeatured: type.isFeatured !== undefined ? type.isFeatured : false,
                                organizationId: new Types.ObjectId(global.config.organization),
                                createdBy: new Types.ObjectId(global.config.organization)
                            });
                            appointmentTypeDocs.push(subType);
                            appointmentTypeNameMap.set(type.name, subType);
                        }
                        formattedAppointmentTypes.push({
                            _id: subType._id,
                            name: type.name,
                            durationInMinutes: type.durationInMinutes,
                            onlineBookingAllowed: type.onlineBookingAllowed !== undefined ? type.onlineBookingAllowed : true,
                            isActive: type.isActive !== undefined ? type.isActive : true,
                            image: type.image || '',
                            isFeatured: type.isFeatured !== undefined ? type.isFeatured : false
                        });
                    }

                    let service = servicesNameMap.get(csvPricing.services.name);
                    if (!service) {
                        service = new Service({
                            organizationId: new Types.ObjectId(global.config.organization),
                            name: csvPricing.services.name,
                            description: "",
                            image: "",
                            classType: csvPricing.services.type,
                            appointmentType: formattedAppointmentTypes,
                            createdBy: new Types.ObjectId(global.config.organization),
                            isFeatured: false
                        });
                        servicesNameMap.set(csvPricing.services.name, service);
                        servicesDocs.push(service);
                    } else {
                        // Update existing service with new appointment types
                        service.appointmentType = [...service.appointmentType, ...formattedAppointmentTypes];
                    }

                    pricingData.services = {
                        type: csvPricing.services.type,
                        serviceCategory: service._id,
                        sessionType: csvPricing.services.sessionType,
                        sessionCount: csvPricing.services.sessionCount,
                        dayPassLimit: csvPricing.services.dayPassLimit,
                        sessionPerDay: csvPricing.services.sessionPerDay,
                        introductoryOffer: csvPricing.services.introductoryOffer,
                        appointmentType: formattedAppointmentTypes.map(type => type._id)
                    };

                }

                return pricingData;
            });

            // Insert appointment types
            if (appointmentTypeDocs.length > 0) {
                await AppointmentType.insertMany(appointmentTypeDocs, { session });
                logger.log(`Successfully migrated ${appointmentTypeDocs.length} appointment types to MongoDB`);
            }

            // Insert services
            if (servicesDocs.length > 0) {
                await Service.insertMany(servicesDocs, { session });
                logger.log(`Successfully migrated ${servicesDocs.length} services to MongoDB`);
            }

            // Insert pricing packages
            const result = await Pricing.insertMany(pricings, { session });
            logger.log(`Successfully migrated ${result.length} pricing packages to MongoDB`);

            // Commit the transaction
            await session.commitTransaction();
            logger.log("Transaction committed successfully");
        } catch (error) {
            // Abort the transaction on error
            if (session) {
                await session.abortTransaction();
            }
            logger.error("Error in migration process:", error);
            throw error;
        }
    } catch (error) {
        logger.error("Error migrating pricing packages:", error);
    } finally {
        // End the session
        if (session) {
            session.endSession();
        }
        // Close the connection
        await closeConnection();
    }
}