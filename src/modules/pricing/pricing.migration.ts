import { Types } from "mongoose";
import logger from "../../common/logger/log.module";
import { connectToMongo, closeConnection } from "../../common/database/db.module";
import { Pricing, SessionType, DurationUnit } from "./pricing.model";
import { Service } from "./service.model";
import { parseCSV } from "../../common/utils/csv-parser";

interface ICsvPricing {
    id: number;
    name: string;
    price: number;
    tax: number;
    durationUnit: DurationUnit;
    isSellOnline: boolean;
    expiredInDays: number;
    hsnOrSacCode?: string;
    isActive?: boolean;
    "serviceId.0": string;
    "serviceId.0.appointmentType.0"?: string;
    "serviceId.0.appointmentType.1"?: string;
    revenueCategory?: string;
}

/**
 * Validate pricing data from CSV
 * @param csvPricing Pricing data from CSV
 * @returns Array of validation error messages
 */
function validatePricingData(csvPricing: ICsvPricing): string[] {
    const errors: string[] = [];

    if (!csvPricing.name) {
        errors.push("Pricing name is required");
    }

    if (csvPricing.price === undefined) {
        errors.push("Price is required");
    } else if (isNaN(csvPricing.price) || csvPricing.price < 0) {
        errors.push("Price must be a non-negative number");
    }

    if (csvPricing.tax === undefined) {
        errors.push("Tax is required");
    } else if (isNaN(csvPricing.tax) || csvPricing.tax < 0) {
        errors.push("Tax must be a non-negative number");
    }

    if (!csvPricing.durationUnit) {
        errors.push("Duration unit is required");
    }

    if (csvPricing.isSellOnline === undefined) {
        errors.push("isSellOnline flag is required");
    }

    if (csvPricing.expiredInDays === undefined) {
        errors.push("Expired in days is required");
    } else if (isNaN(csvPricing.expiredInDays) || csvPricing.expiredInDays <= 0) {
        errors.push("Expired in days must be a positive number");
    }

    if (!csvPricing["serviceId.0"]) {
        errors.push("Service ID is required");
    }

    return errors;
}

export async function migratePricing(_dbName: string = "hop-migration"): Promise<void> {
    let session: any = null;

    try {
        logger.log("Starting pricing migration...");

        // Connect to database
        const mongoose = await connectToMongo();

        // Start a session for transaction
        session = await mongoose.startSession();
        session.startTransaction();

        try {
            // Get pricing data from CSV or other source
            const csvPricings = await parseCSV<ICsvPricing>("data/pricing.csv");

            // Validate data
            const validationErrors: { [key: string]: string[] } = {};
            csvPricings.forEach((pricing, index) => {
                const errors = validatePricingData(pricing);
                if (errors.length > 0) {
                    validationErrors[`Row ${index + 1}`] = errors;
                }
            });

            if (Object.keys(validationErrors).length > 0) {
                logger.error("Validation errors in pricing data:", validationErrors);
                throw new Error("Invalid pricing data in CSV");
            }

            // Get services data to create mapping from CSV ID to service name
            const csvServices = await parseCSV<any>("data/services.csv");

            // Create mapping from CSV service ID to service name
            const csvServiceIdToName: { [csvId: string]: string } = {};
            csvServices.forEach((service: any) => {
                csvServiceIdToName[service.id] = service.name;
            });

            // Get all services from database to create name to ObjectId mapping
            const dbServices = await Service.find({}, { _id: 1, name: 1 }).session(session);
            const serviceNameToObjectId: { [name: string]: string } = {};
            dbServices.forEach(service => {
                serviceNameToObjectId[service.name] = service._id.toString();
            });

            logger.log("Service mapping for pricing:");
            Object.entries(csvServiceIdToName).forEach(([csvId, name]) => {
                const objectId = serviceNameToObjectId[name];
                logger.log(`  CSV ID ${csvId} (${name}) -> MongoDB ObjectId ${objectId}`);
            });

            // Get appointment types data to create mapping from CSV ID to appointment type data
            const csvAppointmentTypes = await parseCSV<any>("data/appointment-types.csv");
            const appointmentTypeIdToData: { [id: string]: any } = {};
            csvAppointmentTypes.forEach((apt: any) => {
                appointmentTypeIdToData[apt.id] = apt;
            });

            // Get full service data to populate pricing services field
            const serviceDataMap: { [serviceId: string]: any } = {};
            for (const csvPricing of csvPricings) {
                const serviceName = csvServiceIdToName[csvPricing["serviceId.0"]];
                const serviceObjectId = serviceNameToObjectId[serviceName];

                if (!serviceObjectId) {
                    throw new Error(`Service not found for CSV ID ${csvPricing["serviceId.0"]} (${serviceName})`);
                }

                // Get the full service data from database
                const serviceData = await Service.findById(serviceObjectId).session(session);
                if (!serviceData) {
                    throw new Error(`Service data not found in database for ObjectId ${serviceObjectId}`);
                }

                serviceDataMap[csvPricing["serviceId.0"]] = serviceData;
            }

            // Transform data for MongoDB
            const pricings = csvPricings.map((csvPricing) => {
                const serviceData = serviceDataMap[csvPricing["serviceId.0"]];
                const csvServiceData = csvServices.find((s: any) => s.id === csvPricing["serviceId.0"]);

                // Get appointment types for this pricing
                const appointmentTypeIds = [];
                if (csvPricing["serviceId.0.appointmentType.0"]) {
                    appointmentTypeIds.push(csvPricing["serviceId.0.appointmentType.0"]);
                }
                if (csvPricing["serviceId.0.appointmentType.1"]) {
                    appointmentTypeIds.push(csvPricing["serviceId.0.appointmentType.1"]);
                }

                // Find matching appointment types from the service's embedded appointment types
                const selectedAppointmentTypes = [];
                appointmentTypeIds.forEach(aptId => {
                    const aptData = appointmentTypeIdToData[aptId];
                    if (aptData) {
                        // Find the matching appointment type in the service's embedded appointment types
                        const matchingApt = serviceData.appointmentType.find((apt: any) =>
                            apt.name === aptData.name && apt.durationInMinutes === parseInt(aptData.durationInMinutes)
                        );
                        if (matchingApt) {
                            selectedAppointmentTypes.push(matchingApt._id);
                        }
                    }
                });

                logger.log(`Pricing "${csvPricing.name}" will reference ${selectedAppointmentTypes.length} appointment types`);

                return new Pricing({
                    id: csvPricing.id,
                    name: csvPricing.name,
                    price: csvPricing.price,
                    tax: csvPricing.tax,
                    durationUnit: csvPricing.durationUnit,
                    isSellOnline: csvPricing.isSellOnline,
                    expiredInDays: csvPricing.expiredInDays,
                    hsnOrSacCode: csvPricing.hsnOrSacCode,
                    isActive: csvPricing.isActive !== undefined ? csvPricing.isActive : true,
                    createdBy: new Types.ObjectId(global.config.organization),
                    revenueCategory: csvPricing.revenueCategory,
                    organizationId: new Types.ObjectId(global.config.organization),
                    // Use actual service data
                    services: {
                        type: serviceData.classType,
                        serviceCategory: serviceData._id, // Reference the service itself as category
                        sessionType: csvServiceData.sessionType as SessionType,
                        sessionCount: csvServiceData.sessionCount,
                        dayPassLimit: csvServiceData.dayPassLimit,
                        sessionPerDay: csvServiceData.sessionPerDay,
                        introductoryOffer: csvServiceData.introductoryOffer,
                        appointmentType: selectedAppointmentTypes // Populate with selected appointment types
                    }
                });
            });

            // Insert pricing packages
            const result = await Pricing.insertMany(pricings, { session });
            logger.log(`Successfully migrated ${result.length} pricing packages to MongoDB`);

            // Commit the transaction
            await session.commitTransaction();
            logger.log("Transaction committed successfully");
        } catch (error) {
            // Abort the transaction on error
            if (session) {
                await session.abortTransaction();
            }
            logger.error("Error in migration process:", error);
            throw error;
        }
    } catch (error) {
        logger.error("Error migrating pricing packages:", error);
    } finally {
        // End the session
        if (session) {
            session.endSession();
        }
        // Close the connection
        await closeConnection();
    }
}