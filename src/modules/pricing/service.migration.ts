import { Types } from "mongoose";
import logger from "../../common/logger/log.module";
import { connectToMongo, closeConnection } from "../../common/database/db.module";
import { Service } from "./service.model";
import { parseCSV } from "../../common/utils/csv-parser";
import { ClassType } from "./pricing.model";

interface ICsvAppointmentType {
  name: string;
  durationInMinutes: number;
  onlineBookingAllowed: boolean;
  isActive?: boolean;
  image?: string;
  isFeatured?: boolean;
}

interface ICsvService {
  name: string;
  description?: string;
  image?: string;
  classType: string;
  appointmentTypes?: string; // JSON string of appointment types
  isFeatured?: boolean;
  isActive?: boolean;
}

/**
 * Validate service data from CSV
 * @param csvService Service data from CSV
 * @returns Array of validation error messages
 */
function validateServiceData(csvService: ICsvService): string[] {
  const errors: string[] = [];

  if (!csvService.name) {
    errors.push("Service name is required");
  }

  if (!csvService.classType) {
    errors.push("Class type is required");
  } else if (!Object.values(ClassType).includes(csvService.classType as ClassType)) {
    errors.push(`Invalid class type: ${csvService.classType}. Valid values are: ${Object.values(ClassType).join(', ')}`);
  }

  // Validate appointment types if provided
  if (csvService.appointmentTypes) {
    try {
      const appointmentTypes = JSON.parse(csvService.appointmentTypes);
      if (!Array.isArray(appointmentTypes)) {
        errors.push("Appointment types must be a valid JSON array");
      } else {
        appointmentTypes.forEach((type: ICsvAppointmentType, index: number) => {
          if (!type.name) {
            errors.push(`Appointment type at index ${index} is missing name`);
          }
          if (type.durationInMinutes === undefined) {
            errors.push(`Appointment type at index ${index} is missing durationInMinutes`);
          }
          if (type.onlineBookingAllowed === undefined) {
            errors.push(`Appointment type at index ${index} is missing onlineBookingAllowed`);
          }
        });
      }
    } catch (error) {
      errors.push("Invalid JSON format for appointment types");
    }
  }

  return errors;
}

/**
 * Migrate services data from CSV to MongoDB
 */
export async function migrateServices(dbName: string = "hop-migration"): Promise<void> {
  let session: any = null;

  try {
    logger.log("Starting services migration...");

    // Connect to database
    await connectToMongo();

    // Start a session for transaction
    const mongoose = require("mongoose");
    session = await mongoose.startSession();
    session.startTransaction();

    try {
      // Get services data from CSV
      const csvServices = await parseCSV<ICsvService>("data/services.csv");

      // Validate data
      const validationErrors: { [key: string]: string[] } = {};
      csvServices.forEach((service, index) => {
        const errors = validateServiceData(service);
        if (errors.length > 0) {
          validationErrors[`Row ${index + 1}`] = errors;
        }
      });

      if (Object.keys(validationErrors).length > 0) {
        logger.error("Validation errors in services data:", validationErrors);
        throw new Error("Invalid services data in CSV");
      }

      // Transform data for MongoDB
      const services = csvServices.map((csvService) => {
        let appointmentTypes: ICsvAppointmentType[] = [];
        
        if (csvService.appointmentTypes) {
          try {
            appointmentTypes = JSON.parse(csvService.appointmentTypes);
          } catch (error) {
            // This should not happen as we've already validated the JSON
            logger.error("Error parsing appointment types:", error);
          }
        }

        return {
          name: csvService.name,
          description: csvService.description || "",
          image: csvService.image,
          classType: csvService.classType,
          appointmentType: appointmentTypes.map(type => ({
            name: type.name,
            durationInMinutes: type.durationInMinutes,
            onlineBookingAllowed: type.onlineBookingAllowed,
            isActive: type.isActive !== undefined ? type.isActive : true,
            image: type.image,
            isFeatured: type.isFeatured !== undefined ? type.isFeatured : false
          })),
          isFeatured: csvService.isFeatured !== undefined ? csvService.isFeatured : false,
          createdBy: new Types.ObjectId(global.config.organization),
          organizationId: new Types.ObjectId(global.config.organization)
        };
      });

      // Insert services
      const result = await Service.insertMany(services, { session });
      logger.log(`Successfully migrated ${result.length} services to MongoDB`);

      // Commit the transaction
      await session.commitTransaction();
      logger.log("Transaction committed successfully");
    } catch (error) {
      // Abort the transaction on error
      if (session) {
        await session.abortTransaction();
      }
      logger.error("Error in migration process:", error);
      throw error;
    }
  } catch (error) {
    logger.error("Error migrating services:", error);
  } finally {
    // End the session
    if (session) {
      session.endSession();
    }
    // Close the connection
    await closeConnection();
  }
}
