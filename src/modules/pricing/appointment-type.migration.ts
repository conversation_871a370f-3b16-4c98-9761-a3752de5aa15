import logger from "../../common/logger/log.module";
import { connectToMongo, closeConnection } from "../../common/database/db.module";
import { Service } from "./service.model";
import { parseCSV } from "../../common/utils/csv-parser";

interface ICsvAppointmentType {
  id: string;
  serviceId: string;
  name: string;
  durationInMinutes: number;
  isActive?: boolean;
}

interface ICsvService {
  id: string;
  name: string;
  type: string;
  serviceCategory: string;
  sessionType: string;
  sessionCount: number;
  dayPassLimit: number;
  sessionPerDay: number;
  introductoryOffer?: string;
}

/**
 * Validate appointment type data from CSV
 * @param csvAppointmentType Appointment type data from CSV
 * @returns Array of validation error messages
 */
function validateAppointmentTypeData(csvAppointmentType: ICsvAppointmentType): string[] {
  const errors: string[] = [];

  if (!csvAppointmentType.id) {
    errors.push("Appointment type ID is required");
  }

  if (!csvAppointmentType.serviceId) {
    errors.push("Service ID is required");
  }

  if (!csvAppointmentType.name) {
    errors.push("Appointment type name is required");
  }

  if (csvAppointmentType.durationInMinutes === undefined) {
    errors.push("Duration in minutes is required");
  } else if (isNaN(csvAppointmentType.durationInMinutes) || csvAppointmentType.durationInMinutes <= 0) {
    errors.push("Duration in minutes must be a positive number");
  }

  return errors;
}

/**
 * Migrate appointment types data from CSV to MongoDB
 * This function adds appointment types to existing services as embedded documents
 */
export async function migrateAppointmentTypes(_dbName: string = "hop-migration"): Promise<void> {
  let session: any = null;

  try {
    logger.log("Starting appointment types migration...");

    // Connect to database
    await connectToMongo();

    // Start a session for transaction
    const mongoose = require("mongoose");
    session = await mongoose.startSession();
    session.startTransaction();

    try {
      // Get services data to create mapping from CSV ID to service name
      const csvServices = await parseCSV<ICsvService>("data/services.csv");

      // Get appointment types data from CSV
      const csvAppointmentTypes = await parseCSV<ICsvAppointmentType>("data/appointment-types.csv");

      // Validate data
      const validationErrors: { [key: string]: string[] } = {};
      csvAppointmentTypes.forEach((appointmentType, index) => {
        const errors = validateAppointmentTypeData(appointmentType);
        if (errors.length > 0) {
          validationErrors[`Row ${index + 1}`] = errors;
        }
      });

      if (Object.keys(validationErrors).length > 0) {
        logger.error("Validation errors in appointment types data:", validationErrors);
        throw new Error("Invalid appointment types data in CSV");
      }

      // Create mapping from CSV service ID to service name
      const csvServiceIdToName: { [csvId: string]: string } = {};
      csvServices.forEach(service => {
        csvServiceIdToName[service.id] = service.name;
      });

      // Get all services from database to create name to ObjectId mapping
      const dbServices = await Service.find({}, { _id: 1, name: 1 }).session(session);
      const serviceNameToObjectId: { [name: string]: string } = {};
      dbServices.forEach(service => {
        serviceNameToObjectId[service.name] = service._id.toString();
      });

      logger.log("Service mapping created:");
      Object.entries(csvServiceIdToName).forEach(([csvId, name]) => {
        const objectId = serviceNameToObjectId[name];
        logger.log(`  CSV ID ${csvId} (${name}) -> MongoDB ObjectId ${objectId}`);
      });

      // Group appointment types by MongoDB service ObjectId
      const appointmentTypesByService: { [mongoServiceId: string]: ICsvAppointmentType[] } = {};

      csvAppointmentTypes.forEach(appointmentType => {
        if (appointmentType.serviceId) {
          // Convert CSV service ID to service name, then to MongoDB ObjectId
          const serviceName = csvServiceIdToName[appointmentType.serviceId];
          const mongoServiceId = serviceNameToObjectId[serviceName];

          if (mongoServiceId) {
            if (!appointmentTypesByService[mongoServiceId]) {
              appointmentTypesByService[mongoServiceId] = [];
            }
            appointmentTypesByService[mongoServiceId].push(appointmentType);
          } else {
            logger.warn(`Could not find MongoDB ObjectId for CSV service ID ${appointmentType.serviceId} (${serviceName})`);
          }
        }
      });

      let totalUpdated = 0;

      // Update services with appointment types
      for (const [mongoServiceId, appointmentTypes] of Object.entries(appointmentTypesByService)) {
        const formattedAppointmentTypes = appointmentTypes.map(type => ({
          name: type.name,
          durationInMinutes: type.durationInMinutes,
          onlineBookingAllowed: true, // Default to true
          isActive: type.isActive !== undefined ? type.isActive : true,
          image: "",
          isFeatured: false
        }));

        const result = await Service.findByIdAndUpdate(
          mongoServiceId,
          { $push: { appointmentType: { $each: formattedAppointmentTypes } } },
          { session, new: true }
        );

        if (result) {
          totalUpdated++;
          const serviceName = result.name;
          logger.log(`Updated service "${serviceName}" (${mongoServiceId}) with ${appointmentTypes.length} appointment types`);
        } else {
          logger.warn(`Service with MongoDB ObjectId ${mongoServiceId} not found`);
        }
      }

      // Commit the transaction
      await session.commitTransaction();
      logger.log("Transaction committed successfully");
      logger.log(`Successfully migrated ${csvAppointmentTypes.length} appointment types to ${totalUpdated} services`);
    } catch (error) {
      // Abort the transaction on error
      if (session) {
        await session.abortTransaction();
      }
      logger.error("Error in migration process:", error);
      throw error;
    }
  } catch (error) {
    logger.error("Error migrating appointment types:", error);
  } finally {
    // End the session
    if (session) {
      session.endSession();
    }
    // Close the connection
    await closeConnection();
  }
}
